"use client";

import { useQuery } from "@tanstack/react-query";
import { getCurrencies } from "@/app/actions/currencies";
import { useProfile } from "@/hooks/useProfile";
import { createClient } from "@/utils/supabase/client";
import { useEffect, useState } from "react";

export default function TestCurrenciesPage() {
  const { data: profile, isLoading: isLoadingProfile } = useProfile();
  const [authState, setAuthState] = useState(null);

  useEffect(() => {
    const checkAuth = async () => {
      const supabase = createClient();
      const { data: { user }, error } = await supabase.auth.getUser();
      setAuthState({ user, error });
    };
    checkAuth();
  }, []);

  const { data: currencies, isLoading: isLoadingCurrencies, error: currencyError } = useQuery({
    queryKey: ["test-currencies", profile?.pricing_tier],
    queryFn: async () => {
      try {
        const tier = profile?.pricing_tier || "basic";
        console.log("🧪 TEST: getCurrencies called with tier:", tier, "profile:", profile);
        const result = await getCurrencies(tier);
        console.log("🧪 TEST: getCurrencies returned:", Object.keys(result || {}).length, "currencies");
        return result;
      } catch (error) {
        console.error("🧪 TEST: getCurrencies error:", error);
        throw error;
      }
    },
    enabled: true, // Always enabled for testing
  });

  console.log("🧪 TEST: Component state:", {
    profile,
    isLoadingProfile,
    currencies,
    isLoadingCurrencies,
    currencyError,
    currencyCount: currencies ? Object.keys(currencies).length : 0
  });

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-4">Currency Test Page</h1>

      <div className="space-y-4">
        <div className="card bg-base-200 p-4">
          <h2 className="text-lg font-semibold mb-2">Auth State</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(authState, null, 2)}
          </pre>
        </div>

        <div className="card bg-base-200 p-4">
          <h2 className="text-lg font-semibold mb-2">Profile Data</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(profile, null, 2)}
          </pre>
        </div>

        <div className="card bg-base-200 p-4">
          <h2 className="text-lg font-semibold mb-2">Currency Data</h2>
          <p>Loading: {isLoadingCurrencies ? "Yes" : "No"}</p>
          <p>Count: {currencies ? Object.keys(currencies).length : 0}</p>
          {currencyError && (
            <p className="text-error">Error: {currencyError.message}</p>
          )}
          <pre className="text-sm overflow-auto max-h-96">
            {JSON.stringify(currencies, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
}
